import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

const sampleBlogPosts = [
  {
    title: "The Future of IT Consulting: Trends to Watch in 2025",
    slug: "future-of-it-consulting-2025",
    excerpt: "Explore the emerging trends and technologies that will shape the IT consulting landscape in 2025 and beyond.",
    content: `
      <h2>Introduction</h2>
      <p>The IT consulting industry is evolving rapidly, driven by technological advancements and changing business needs. As we look ahead to 2025, several key trends are emerging that will reshape how IT consultants work and deliver value to their clients.</p>
      
      <h3>1. Artificial Intelligence Integration</h3>
      <p>AI is no longer a futuristic concept—it's becoming an integral part of business operations. IT consultants must understand how to implement AI solutions that drive real business value.</p>
      
      <blockquote>
        <p>"The companies that will thrive in 2025 are those that successfully integrate AI into their core business processes."</p>
      </blockquote>
      
      <h3>2. Cloud-First Strategies</h3>
      <p>The shift to cloud computing continues to accelerate. Modern IT consulting requires expertise in:</p>
      <ul>
        <li>Multi-cloud architectures</li>
        <li>Cloud security best practices</li>
        <li>Cost optimization strategies</li>
        <li>Migration planning and execution</li>
      </ul>
      
      <h3>3. Cybersecurity as a Priority</h3>
      <p>With increasing cyber threats, security can no longer be an afterthought. IT consultants must embed security considerations into every aspect of their recommendations.</p>
      
      <h2>Conclusion</h2>
      <p>The future of IT consulting is bright for those who adapt to these emerging trends. By staying ahead of the curve, consultants can continue to provide exceptional value to their clients.</p>
    `,
    featuredImage: "/assets/img/blog/future-it-consulting.jpg",
    status: "PUBLISHED",
    publishedAt: new Date("2024-12-15T10:00:00Z"),
    readTime: 5,
    seoTitle: "Future of IT Consulting: 2025 Trends & Predictions",
    seoDescription: "Discover the key trends shaping IT consulting in 2025, including AI integration, cloud strategies, and cybersecurity priorities.",
    categoryName: "Technology Trends",
    tags: ["IT Consulting", "AI", "Cloud Computing", "Cybersecurity", "2025 Trends"]
  },
  {
    title: "Digital Transformation Success Stories: Lessons from the Field",
    slug: "digital-transformation-success-stories",
    excerpt: "Real-world examples of successful digital transformation projects and the key factors that made them successful.",
    content: `
      <h2>Case Study 1: Manufacturing Company Modernization</h2>
      <p>A traditional manufacturing company transformed their operations by implementing IoT sensors and data analytics platforms.</p>
      
      <h3>The Challenge</h3>
      <p>The company was struggling with:</p>
      <ul>
        <li>Inefficient production processes</li>
        <li>High maintenance costs</li>
        <li>Limited visibility into operations</li>
      </ul>
      
      <h3>The Solution</h3>
      <p>We implemented a comprehensive IoT solution that included:</p>
      <ol>
        <li>Smart sensors on critical equipment</li>
        <li>Real-time data dashboard</li>
        <li>Predictive maintenance algorithms</li>
        <li>Mobile alerts for operators</li>
      </ol>
      
      <h3>Results</h3>
      <p>The transformation delivered impressive results:</p>
      <ul>
        <li><strong>30% reduction</strong> in maintenance costs</li>
        <li><strong>25% increase</strong> in production efficiency</li>
        <li><strong>90% decrease</strong> in unplanned downtime</li>
      </ul>
      
      <h2>Key Success Factors</h2>
      <p>Based on our experience with multiple digital transformation projects, we've identified several critical success factors:</p>
      
      <h3>1. Leadership Commitment</h3>
      <p>Successful transformations require strong leadership support and clear vision from the top.</p>
      
      <h3>2. Employee Engagement</h3>
      <p>Change management is crucial. Employees need to understand the benefits and be trained on new systems.</p>
      
      <h3>3. Phased Approach</h3>
      <p>Breaking the transformation into manageable phases allows for learning and adjustment along the way.</p>
      
      <blockquote>
        <p>"Digital transformation is not just about technology—it's about people, processes, and culture."</p>
      </blockquote>
    `,
    featuredImage: "/assets/img/blog/digital-transformation.jpg",
    status: "PUBLISHED",
    publishedAt: new Date("2024-12-10T14:30:00Z"),
    readTime: 7,
    seoTitle: "Digital Transformation Success Stories & Best Practices",
    seoDescription: "Learn from real digital transformation case studies and discover the key factors that drive successful technology implementations.",
    categoryName: "Case Studies",
    tags: ["Digital Transformation", "Case Studies", "IoT", "Manufacturing", "Success Stories"]
  },
  {
    title: "Cloud Security Best Practices for Small and Medium Businesses",
    slug: "cloud-security-best-practices-smb",
    excerpt: "Essential cloud security practices that small and medium businesses should implement to protect their data and operations.",
    content: `
      <h2>Why Cloud Security Matters for SMBs</h2>
      <p>Small and medium businesses are increasingly targeted by cybercriminals because they often have weaker security measures than large enterprises. Moving to the cloud doesn't automatically make you more secure—you need to implement proper security practices.</p>
      
      <h2>Essential Security Practices</h2>
      
      <h3>1. Multi-Factor Authentication (MFA)</h3>
      <p>MFA is one of the most effective ways to prevent unauthorized access to your cloud accounts.</p>
      
      <pre><code>// Example: Enabling MFA in AWS CLI
aws iam create-virtual-mfa-device --virtual-mfa-device-name MyMFADevice</code></pre>
      
      <h3>2. Regular Security Audits</h3>
      <p>Conduct regular security assessments to identify vulnerabilities and ensure compliance with security policies.</p>
      
      <h3>3. Data Encryption</h3>
      <p>Encrypt data both in transit and at rest. Most cloud providers offer built-in encryption services.</p>
      
      <h3>4. Access Control</h3>
      <p>Implement the principle of least privilege—users should only have access to the resources they need.</p>
      
      <h2>Common Security Mistakes to Avoid</h2>
      <ul>
        <li>Using default passwords</li>
        <li>Overprivileged user accounts</li>
        <li>Unencrypted data storage</li>
        <li>Lack of monitoring and logging</li>
        <li>Ignoring security updates</li>
      </ul>
      
      <h2>Security Monitoring Tools</h2>
      <p>Consider implementing these monitoring solutions:</p>
      <ul>
        <li><strong>AWS CloudTrail</strong> - For AWS environments</li>
        <li><strong>Azure Security Center</strong> - For Microsoft Azure</li>
        <li><strong>Google Cloud Security Command Center</strong> - For Google Cloud</li>
      </ul>
      
      <h2>Conclusion</h2>
      <p>Cloud security is an ongoing process, not a one-time setup. By implementing these best practices and staying vigilant, SMBs can significantly reduce their security risks while enjoying the benefits of cloud computing.</p>
    `,
    featuredImage: "/assets/img/blog/cloud-security.jpg",
    status: "PUBLISHED",
    publishedAt: new Date("2024-12-05T09:15:00Z"),
    readTime: 6,
    seoTitle: "Cloud Security Best Practices for SMBs | Complete Guide",
    seoDescription: "Comprehensive guide to cloud security for small and medium businesses. Learn essential practices to protect your data and operations.",
    categoryName: "Security",
    tags: ["Cloud Security", "SMB", "Cybersecurity", "Best Practices", "Data Protection"]
  }
]

async function seedBlogPosts() {
  try {
    console.log('Starting blog post seeding...')

    // First, create a default user if none exists
    let user = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    })

    if (!user) {
      user = await prisma.user.create({
        data: {
          name: 'Admin User',
          email: '<EMAIL>',
          password: 'hashed_password_here', // In real app, this should be properly hashed
          role: 'ADMIN',
          image: '/assets/img/team/admin-avatar.jpg'
        }
      })
      console.log('Created default admin user')
    }

    // Create categories
    const categories = await Promise.all([
      prisma.category.upsert({
        where: { slug: 'technology-trends' },
        update: {},
        create: {
          name: 'Technology Trends',
          slug: 'technology-trends',
          description: 'Latest trends and innovations in technology',
          color: '#3b82f6',
          createdBy: user.id
        }
      }),
      prisma.category.upsert({
        where: { slug: 'case-studies' },
        update: {},
        create: {
          name: 'Case Studies',
          slug: 'case-studies',
          description: 'Real-world project examples and success stories',
          color: '#10b981',
          createdBy: user.id
        }
      }),
      prisma.category.upsert({
        where: { slug: 'security' },
        update: {},
        create: {
          name: 'Security',
          slug: 'security',
          description: 'Cybersecurity insights and best practices',
          color: '#ef4444',
          createdBy: user.id
        }
      })
    ])

    console.log('Created/updated categories')

    // Create tags
    const allTags = [...new Set(sampleBlogPosts.flatMap(post => post.tags))]
    const tags = await Promise.all(
      allTags.map(tagName =>
        prisma.tag.upsert({
          where: { slug: tagName.toLowerCase().replace(/\s+/g, '-') },
          update: {},
          create: {
            name: tagName,
            slug: tagName.toLowerCase().replace(/\s+/g, '-'),
            color: `#${Math.floor(Math.random()*16777215).toString(16)}`,
            createdBy: user.id
          }
        })
      )
    )

    console.log('Created/updated tags')

    // Create blog posts
    for (const postData of sampleBlogPosts) {
      const category = categories.find(cat => cat.name === postData.categoryName)
      const postTags = tags.filter(tag => 
        postData.tags.some(postTag => 
          postTag.toLowerCase().replace(/\s+/g, '-') === tag.slug
        )
      )

      const existingPost = await prisma.post.findUnique({
        where: { slug: postData.slug }
      })

      if (!existingPost) {
        await prisma.post.create({
          data: {
            title: postData.title,
            slug: postData.slug,
            excerpt: postData.excerpt,
            content: postData.content,
            featuredImage: postData.featuredImage,
            status: postData.status as any,
            publishedAt: postData.publishedAt,
            seoTitle: postData.seoTitle,
            seoDescription: postData.seoDescription,
            authorId: user.id,
            categoryId: category?.id,
            tags: {
              connect: postTags.map(tag => ({ id: tag.id }))
            }
          }
        })
        console.log(`Created blog post: ${postData.title}`)
      } else {
        console.log(`Blog post already exists: ${postData.title}`)
      }
    }

    console.log('Blog post seeding completed successfully!')
  } catch (error) {
    console.error('Error seeding blog posts:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

// Run the seed function
if (require.main === module) {
  seedBlogPosts()
    .catch((error) => {
      console.error(error)
      process.exit(1)
    })
}

export default seedBlogPosts
