/* Blog Content Styling - Frontend Design System Integration */

/* ===== BLOG CONTENT AREA ===== */
.blog-content-area {
  font-family: 'Manrope', sans-serif;
  line-height: 1.8;
  color: #333;
  font-size: 16px;
}

/* ===== HEADINGS ===== */
.blog-content-area h1 {
  font-family: 'Montserrat', sans-serif;
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-top: 2rem;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.blog-content-area h2 {
  font-family: 'Montserrat', sans-serif;
  font-size: 2rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-top: 2rem;
  margin-bottom: 1rem;
  line-height: 1.3;
  border-bottom: 2px solid #e89d1a;
  padding-bottom: 0.5rem;
}

.blog-content-area h3 {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  line-height: 1.4;
}

.blog-content-area h4 {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  line-height: 1.4;
}

.blog-content-area h5 {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.blog-content-area h6 {
  font-family: 'Montserrat', sans-serif;
  font-size: 1rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

/* ===== PARAGRAPHS ===== */
.blog-content-area p {
  margin-bottom: 1.5rem;
  line-height: 1.8;
  color: #555;
  font-size: 16px;
}

.blog-content-area p:last-child {
  margin-bottom: 0;
}

/* ===== LINKS ===== */
.blog-content-area a {
  color: #03276e;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.blog-content-area a:hover {
  color: #e89d1a;
  text-decoration: underline;
}

/* ===== LISTS ===== */
.blog-content-area ul,
.blog-content-area ol {
  margin-bottom: 1.5rem;
  padding-left: 2rem;
}

.blog-content-area ul li,
.blog-content-area ol li {
  margin-bottom: 0.5rem;
  line-height: 1.7;
  color: #555;
}

.blog-content-area ul li {
  list-style-type: disc;
}

.blog-content-area ol li {
  list-style-type: decimal;
}

.blog-content-area ul ul,
.blog-content-area ol ol,
.blog-content-area ul ol,
.blog-content-area ol ul {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

/* ===== BLOCKQUOTES ===== */
.blog-content-area blockquote {
  background: #f8f9fa;
  border-left: 4px solid #03276e;
  margin: 2rem 0;
  padding: 1.5rem 2rem;
  font-style: italic;
  font-size: 1.1rem;
  line-height: 1.6;
  color: #444;
  border-radius: 0 8px 8px 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.blog-content-area blockquote p {
  margin-bottom: 0;
  font-weight: 500;
}

.blog-content-area blockquote p:last-child {
  margin-bottom: 0;
}

/* ===== CODE BLOCKS ===== */
.blog-content-area code {
  background: #f1f3f4;
  padding: 0.2rem 0.4rem;
  border-radius: 4px;
  font-family: 'JetBrains Mono', 'Courier New', monospace;
  font-size: 0.9rem;
  color: #d63384;
  font-weight: 500;
}

.blog-content-area pre {
  background: #2d3748;
  color: #e2e8f0;
  padding: 1.5rem;
  border-radius: 8px;
  overflow-x: auto;
  margin: 1.5rem 0;
  font-family: 'JetBrains Mono', 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.blog-content-area pre code {
  background: none;
  padding: 0;
  color: inherit;
  font-size: inherit;
}

/* ===== IMAGES ===== */
.blog-content-area img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 2rem auto;
  display: block;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* ===== TABLES ===== */
.blog-content-area table {
  width: 100%;
  border-collapse: collapse;
  margin: 2rem 0;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.blog-content-area th,
.blog-content-area td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
}

.blog-content-area th {
  background: #f8f9fa;
  font-weight: 600;
  color: #1a1a1a;
  font-family: 'Montserrat', sans-serif;
}

.blog-content-area tr:last-child td {
  border-bottom: none;
}

.blog-content-area tr:hover {
  background: #f8f9fa;
}

/* ===== EMPHASIS ===== */
.blog-content-area strong {
  font-weight: 700;
  color: #1a1a1a;
}

.blog-content-area em {
  font-style: italic;
}

.blog-content-area mark {
  background: #fff3cd;
  padding: 0.1rem 0.3rem;
  border-radius: 3px;
}

.blog-content-area u {
  text-decoration: underline;
  text-decoration-color: #03276e;
  text-decoration-thickness: 2px;
}

.blog-content-area s {
  text-decoration: line-through;
  color: #6c757d;
}

/* ===== HORIZONTAL RULES ===== */
.blog-content-area hr {
  border: none;
  height: 2px;
  background: linear-gradient(90deg, #03276e, #e89d1a);
  margin: 3rem 0;
  border-radius: 1px;
}

/* ===== TEXT ALIGNMENT ===== */
.blog-content-area .has-text-align-left {
  text-align: left;
}

.blog-content-area .has-text-align-center {
  text-align: center;
}

.blog-content-area .has-text-align-right {
  text-align: right;
}

.blog-content-area .has-text-align-justify {
  text-align: justify;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .blog-content-area {
    font-size: 15px;
  }
  
  .blog-content-area h1 {
    font-size: 2rem;
  }
  
  .blog-content-area h2 {
    font-size: 1.75rem;
  }
  
  .blog-content-area h3 {
    font-size: 1.375rem;
  }
  
  .blog-content-area h4 {
    font-size: 1.125rem;
  }
  
  .blog-content-area blockquote {
    padding: 1rem 1.5rem;
    margin: 1.5rem 0;
  }
  
  .blog-content-area pre {
    padding: 1rem;
    font-size: 0.8rem;
  }
  
  .blog-content-area ul,
  .blog-content-area ol {
    padding-left: 1.5rem;
  }
}

/* ===== BLOG POST DETAIL COMPONENT STYLES ===== */

/* Back to Blog Link */
.learn {
  color: #03276e;
  text-decoration: none;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: color 0.3s ease;
}

.learn:hover {
  color: #e89d1a;
}

.learn i {
  font-size: 0.9rem;
}

/* Blog Post Header */
.blog-post-header {
  margin-bottom: 3rem;
}

/* Category Badge */
.blog-post-category {
  margin-bottom: 1rem;
}

.category-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.category-badge i {
  font-size: 0.8rem;
}

/* Blog Post Excerpt */
.blog-post-excerpt p {
  font-size: 1.25rem;
  line-height: 1.6;
  color: #666;
  font-style: italic;
  margin: 0;
}

/* Blog Post Meta */
.blog-post-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  align-items: center;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #666;
  font-size: 0.9rem;
}

.meta-item img {
  width: 16px;
  height: 16px;
  opacity: 0.7;
}

.meta-item i {
  font-size: 0.9rem;
  opacity: 0.7;
}

/* Blog Post Tags */
.blog-post-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.tag-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 0.75rem;
  border: 1px solid;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
}

.tag-badge:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tag-badge i {
  font-size: 0.7rem;
}

/* Featured Image */
.blog-post-featured-image {
  margin: 2rem 0;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.blog-post-featured-image img {
  width: 100%;
  height: auto;
  display: block;
  transition: transform 0.3s ease;
}

.blog-post-featured-image:hover img {
  transform: scale(1.02);
}

/* Social Share */
.blog-post-social {
  padding: 1.5rem 0;
  border-top: 1px solid #e9ecef;
  border-bottom: 1px solid #e9ecef;
}

/* Related Posts Section */
.blog-related-posts {
  margin-top: 4rem;
  padding-top: 3rem;
  border-top: 2px solid #e9ecef;
}

.blog-related-posts .heading1 {
  text-align: center;
  margin-bottom: 3rem;
}

.blog-related-posts .heading1 h3 {
  font-family: 'Montserrat', sans-serif;
  font-size: 2rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

/* Author Bio Section */
.blog-author-bio {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 12px;
  margin: 3rem 0;
  border-left: 4px solid #03276e;
}

.author-bio-content {
  display: flex;
  gap: 1.5rem;
  align-items: flex-start;
}

.author-avatar {
  flex-shrink: 0;
}

.author-avatar img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #fff;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.author-info h4 {
  font-family: 'Montserrat', sans-serif;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 0.5rem 0;
}

.author-info p {
  color: #666;
  line-height: 1.6;
  margin: 0;
}

/* Responsive Design for Blog Post Detail */
@media (max-width: 768px) {
  .blog-post-meta {
    gap: 1rem;
  }

  .meta-item {
    font-size: 0.8rem;
  }

  .blog-post-tags {
    gap: 0.5rem;
  }

  .tag-badge {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }

  .blog-post-excerpt p {
    font-size: 1.1rem;
  }

  .author-bio-content {
    flex-direction: column;
    text-align: center;
  }

  .author-avatar {
    align-self: center;
  }
}

/* ===== BLOG POST NAVIGATION ===== */
.blog-post-navigation {
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid #e9ecef;
}

.nav-link {
  display: block;
  padding: 1.5rem;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  text-decoration: none;
  transition: all 0.3s ease;
  background: #fff;
  height: 100%;
}

.nav-link:hover {
  border-color: #03276e;
  box-shadow: 0 4px 15px rgba(3, 39, 110, 0.1);
  transform: translateY(-2px);
}

.nav-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.next-post .nav-label {
  justify-content: flex-end;
  text-align: right;
}

.nav-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.4;
}

.next-post .nav-title {
  text-align: right;
}

.nav-link:hover .nav-title {
  color: #03276e;
}

/* ===== BLOG POST SKELETON ===== */
.blog-post-skeleton {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.skeleton-line {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
  margin-bottom: 0.75rem;
}

.skeleton-line-title {
  height: 40px;
  width: 100%;
}

.skeleton-line-subtitle {
  height: 24px;
  width: 80%;
}

.skeleton-line-text {
  height: 20px;
  width: 90%;
}

.skeleton-line-text-short {
  height: 20px;
  width: 60%;
}

@keyframes loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* ===== RESPONSIVE NAVIGATION ===== */
@media (max-width: 768px) {
  .blog-post-navigation .row {
    flex-direction: column;
    gap: 1rem;
  }

  .next-post .nav-label,
  .next-post .nav-title {
    text-align: left;
    justify-content: flex-start;
  }

  .nav-link {
    padding: 1rem;
  }

  .nav-title {
    font-size: 1rem;
  }
}
