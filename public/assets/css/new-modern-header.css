/*
==================================
NEW MODERN HEADER STYLES
==================================
*/

/* Base Header Styles */
.new-modern-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  padding: 12px 0;
}

/* Sticky Header */
.new-modern-header.sticky {
  position: fixed;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(15px);
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.12);
  border-bottom: 1px solid rgba(0, 0, 0, 0.15);
  padding: 8px 0;
  animation: slideInFromTop 0.3s ease-out;
  will-change: transform;
  transform: translateZ(0); /* Force hardware acceleration */
}

@keyframes slideInFromTop {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}

/* Logo Styles */
.navbar-brand {
  padding: 0;
  margin-right: 2rem;
}

.header-logo {
  height: 48px;
  width: auto;
  transition: all 0.3s ease;
}

.new-modern-header.sticky .header-logo {
  height: 40px;
}

/* Navigation Styles */
.navbar-nav .nav-link {
  color: #333;
  font-weight: 500;
  font-size: 16px;
  padding: 8px 16px;
  margin: 0 4px;
  border-radius: 6px;
  transition: all 0.3s ease;
  position: relative;
}

.navbar-nav .nav-link:hover {
  color: #ff7a01;
  background: rgba(255, 122, 1, 0.1);
}

.navbar-nav .nav-link.active {
  color: #ff7a01;
  background: rgba(255, 122, 1, 0.15);
}

/* Dropdown Styles */
.nav-item.dropdown {
  position: relative;
}

.dropdown-toggle::after {
  display: none;
}

.dropdown-toggle .fa-chevron-down {
  font-size: 12px;
  transition: transform 0.3s ease;
}

.nav-item.dropdown.show .fa-chevron-down {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 280px;
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  padding: 12px 0;
  margin-top: 8px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 1001;
}

.dropdown-menu.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: #333;
  text-decoration: none;
  transition: all 0.2s ease;
  border-radius: 8px;
  margin: 2px 12px;
  font-size: 15px;
}

.dropdown-item:hover {
  background: rgba(255, 122, 1, 0.1);
  color: #ff7a01;
  transform: translateX(4px);
}

.dropdown-item i {
  width: 20px;
  text-align: center;
  opacity: 0.7;
  font-size: 14px;
}

/* CTA Button */
.nav-cta {
  background: linear-gradient(135deg, #ff7a01 0%, #ff9500 100%);
  border: none;
  color: white;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 122, 1, 0.3);
}

.nav-cta:hover {
  background: linear-gradient(135deg, #e66a00 0%, #ff8500 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 122, 1, 0.4);
  color: white;
}

/* Mobile Toggle Button */
.navbar-toggler {
  border: none;
  padding: 8px;
  background: transparent;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.navbar-toggler:focus {
  box-shadow: none;
  outline: 2px solid #ff7a01;
  outline-offset: 2px;
}

/* Hamburger Icon */
.hamburger-icon {
  display: flex;
  flex-direction: column;
  width: 24px;
  height: 18px;
  position: relative;
  cursor: pointer;
}

.hamburger-icon span {
  display: block;
  height: 2px;
  width: 100%;
  background: #333;
  border-radius: 1px;
  transition: all 0.3s ease;
  transform-origin: center;
}

.hamburger-icon span:nth-child(1) {
  margin-bottom: 6px;
}

.hamburger-icon span:nth-child(2) {
  margin-bottom: 6px;
}

.hamburger-icon.active span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.hamburger-icon.active span:nth-child(2) {
  opacity: 0;
}

.hamburger-icon.active span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Overlay */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
  z-index: 1998;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.mobile-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* Mobile Menu */
.mobile-menu {
  position: fixed;
  top: 0;
  right: -100%;
  width: 320px;
  max-width: 85vw;
  height: 100%;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  z-index: 1999;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: -2px 0 20px rgba(0, 0, 0, 0.3);
  overflow-y: auto;
}

.mobile-menu.show {
  right: 0;
}

/* Mobile Menu Header */
.mobile-menu-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-logo-img {
  height: 40px;
  width: auto;
  filter: brightness(0) invert(1);
}

.mobile-close {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.mobile-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Mobile Navigation */
.mobile-nav {
  padding: 20px;
}

.mobile-nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.mobile-nav-list > li {
  margin-bottom: 8px;
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  padding: 16px;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  border-radius: 12px;
  transition: all 0.3s ease;
  font-size: 16px;
  font-weight: 500;
}

.mobile-nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.mobile-nav-link.active {
  background: rgba(255, 122, 1, 0.2);
  color: #ff7a01;
}

.mobile-nav-link i {
  width: 24px;
  margin-right: 12px;
  text-align: center;
  font-size: 16px;
}

/* Mobile Dropdown */
.mobile-dropdown-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mobile-dropdown-toggle {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  color: rgba(255, 255, 255, 0.7);
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  cursor: pointer;
  margin-right: 16px;
}

.mobile-dropdown-toggle:hover {
  background: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.9);
}

.mobile-dropdown-toggle i.rotate {
  transform: rotate(180deg);
}

.mobile-dropdown-menu {
  list-style: none;
  padding: 0;
  margin: 8px 0 0 0;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
  opacity: 0;
}

.mobile-dropdown-menu.show {
  max-height: 300px;
  opacity: 1;
}

.mobile-dropdown-link {
  display: flex;
  align-items: center;
  padding: 12px 16px 12px 52px;
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-size: 14px;
}

.mobile-dropdown-link:hover {
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.9);
}

.mobile-dropdown-link i {
  width: 20px;
  margin-right: 10px;
  text-align: center;
  font-size: 14px;
}

/* Mobile CTA */
.mobile-cta {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive Adjustments */
@media (max-width: 991.98px) {
  .new-modern-header {
    padding: 10px 0;
  }
  
  .new-modern-header.sticky {
    padding: 8px 0;
  }
}

@media (max-width: 575.98px) {
  .mobile-menu {
    width: 100%;
    max-width: 100%;
  }
  
  .header-logo {
    height: 40px;
  }
  
  .new-modern-header.sticky .header-logo {
    height: 36px;
  }
}

/* Body padding when header is sticky */
body.header-sticky {
  padding-top: 80px;
  transition: padding-top 0.3s ease;
}

@media (max-width: 991.98px) {
  body.header-sticky {
    padding-top: 70px;
  }
}

/* Sticky header performance optimizations */
.new-modern-header.sticky * {
  will-change: auto;
}

.new-modern-header.sticky .navbar-nav .nav-link {
  transform: translateZ(0);
}

/* Enhanced sticky header shadow */
.new-modern-header.sticky::before {
  content: '';
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
}

/* Accessibility and Focus Styles */
.nav-link:focus,
.dropdown-item:focus,
.navbar-toggler:focus,
.mobile-nav-link:focus,
.mobile-dropdown-link:focus,
.mobile-dropdown-toggle:focus,
.mobile-close:focus {
  outline: 2px solid #ff7a01;
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(255, 122, 1, 0.2);
}

/* Skip to content link for accessibility */
.skip-to-content {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #ff7a01;
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 10000;
  transition: top 0.3s ease;
}

.skip-to-content:focus {
  top: 6px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .new-modern-header {
    background: white;
    border-bottom: 2px solid black;
  }

  .navbar-nav .nav-link {
    color: black;
  }

  .dropdown-menu {
    border: 2px solid black;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .new-modern-header,
  .dropdown-menu,
  .mobile-menu,
  .mobile-overlay,
  .hamburger-icon span,
  .nav-link,
  .dropdown-item {
    transition: none;
    animation: none;
  }
}
