/**
 * Navigation Testing Utilities
 * This file contains utilities to test navigation functionality
 */

export interface NavigationTestResult {
  test: string;
  passed: boolean;
  message: string;
}

export class NavigationTester {
  private results: NavigationTestResult[] = [];

  /**
   * Test if mobile menu toggle functionality works
   */
  testMobileMenuToggle(): NavigationTestResult {
    const test = "Mobile Menu Toggle";
    
    try {
      const mobileButton = document.querySelector('.mobile-nav-icon');
      const mobileSidebar = document.querySelector('.mobile-sidebar');
      
      if (!mobileButton || !mobileSidebar) {
        return {
          test,
          passed: false,
          message: "Mobile menu elements not found"
        };
      }

      // Check if elements have proper classes and attributes
      const hasProperAttributes = mobileButton.hasAttribute('aria-label') && 
                                 mobileButton.hasAttribute('aria-expanded');
      
      return {
        test,
        passed: hasProperAttributes,
        message: hasProperAttributes ? "Mobile menu toggle is properly configured" : "Missing accessibility attributes"
      };
    } catch (error) {
      return {
        test,
        passed: false,
        message: `Error testing mobile menu: ${error}`
      };
    }
  }

  /**
   * Test if sticky header functionality works
   */
  testStickyHeader(): NavigationTestResult {
    const test = "Sticky Header";
    
    try {
      const header = document.querySelector('.header-area');
      
      if (!header) {
        return {
          test,
          passed: false,
          message: "Header element not found"
        };
      }

      // Check if header has sticky class when scrolled
      const hasSticky = header.classList.contains('sticky');
      const scrollY = window.scrollY;
      
      const shouldBeSticky = scrollY > 100;
      const isCorrectlySticky = shouldBeSticky === hasSticky;
      
      return {
        test,
        passed: isCorrectlySticky,
        message: isCorrectlySticky ? 
          "Sticky header is working correctly" : 
          `Sticky header state mismatch. ScrollY: ${scrollY}, HasSticky: ${hasSticky}`
      };
    } catch (error) {
      return {
        test,
        passed: false,
        message: `Error testing sticky header: ${error}`
      };
    }
  }

  /**
   * Test if dropdown menus are properly configured
   */
  testDropdownMenus(): NavigationTestResult {
    const test = "Dropdown Menus";
    
    try {
      const dropdownParents = document.querySelectorAll('.dropdown-menu-parrent');
      const dropdownMenus = document.querySelectorAll('.dropdown-menu');
      
      if (dropdownParents.length === 0 || dropdownMenus.length === 0) {
        return {
          test,
          passed: false,
          message: "Dropdown elements not found"
        };
      }

      // Check if dropdowns have proper structure
      let allDropdownsValid = true;
      let invalidCount = 0;

      dropdownParents.forEach((parent) => {
        const menu = parent.querySelector('.dropdown-menu');
        if (!menu) {
          allDropdownsValid = false;
          invalidCount++;
        }
      });

      return {
        test,
        passed: allDropdownsValid,
        message: allDropdownsValid ? 
          `All ${dropdownParents.length} dropdown menus are properly configured` :
          `${invalidCount} dropdown menus are missing proper structure`
      };
    } catch (error) {
      return {
        test,
        passed: false,
        message: `Error testing dropdown menus: ${error}`
      };
    }
  }

  /**
   * Test responsive behavior
   */
  testResponsiveBehavior(): NavigationTestResult {
    const test = "Responsive Behavior";
    
    try {
      const desktopNav = document.querySelector('.main-menu-ex');
      const mobileHeader = document.querySelector('.mobile-header');
      
      if (!desktopNav || !mobileHeader) {
        return {
          test,
          passed: false,
          message: "Navigation elements not found"
        };
      }

      const screenWidth = window.innerWidth;
      const isMobile = screenWidth < 992;
      
      // Check computed styles
      const desktopNavStyle = window.getComputedStyle(desktopNav);
      const mobileHeaderStyle = window.getComputedStyle(mobileHeader);
      
      const desktopHidden = desktopNavStyle.display === 'none';
      const mobileVisible = mobileHeaderStyle.display !== 'none';
      
      const correctResponsive = isMobile ? (desktopHidden && mobileVisible) : (!desktopHidden);
      
      return {
        test,
        passed: correctResponsive,
        message: correctResponsive ? 
          `Responsive behavior is correct for ${screenWidth}px screen` :
          `Responsive behavior issue at ${screenWidth}px. Desktop hidden: ${desktopHidden}, Mobile visible: ${mobileVisible}`
      };
    } catch (error) {
      return {
        test,
        passed: false,
        message: `Error testing responsive behavior: ${error}`
      };
    }
  }

  /**
   * Run all navigation tests
   */
  runAllTests(): NavigationTestResult[] {
    this.results = [];
    
    this.results.push(this.testMobileMenuToggle());
    this.results.push(this.testStickyHeader());
    this.results.push(this.testDropdownMenus());
    this.results.push(this.testResponsiveBehavior());
    
    return this.results;
  }

  /**
   * Get test results summary
   */
  getTestSummary(): { total: number; passed: number; failed: number; passRate: number } {
    const total = this.results.length;
    const passed = this.results.filter(r => r.passed).length;
    const failed = total - passed;
    const passRate = total > 0 ? (passed / total) * 100 : 0;
    
    return { total, passed, failed, passRate };
  }

  /**
   * Log test results to console
   */
  logResults(): void {
    console.group('🧪 Navigation Tests Results');
    
    this.results.forEach(result => {
      const icon = result.passed ? '✅' : '❌';
      console.log(`${icon} ${result.test}: ${result.message}`);
    });
    
    const summary = this.getTestSummary();
    console.log(`\n📊 Summary: ${summary.passed}/${summary.total} tests passed (${summary.passRate.toFixed(1)}%)`);
    
    console.groupEnd();
  }
}

// Export a default instance for easy use
export const navigationTester = new NavigationTester();

// Auto-run tests in development mode
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  // Run tests after DOM is loaded
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(() => {
        navigationTester.runAllTests();
        navigationTester.logResults();
      }, 1000);
    });
  } else {
    setTimeout(() => {
      navigationTester.runAllTests();
      navigationTester.logResults();
    }, 1000);
  }
}
