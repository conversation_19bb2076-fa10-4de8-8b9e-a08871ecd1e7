/* Milestone Detail Page Styles */

.milestone-detail {
  background: #fff;
}

.milestone-detail-content {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Overview Section */
.milestone-overview {
  background: linear-gradient(135deg, #010E5D 0%, #03276e 100%);
  color: #ffffff;
  padding: 60px 40px;
  text-align: center;
  position: relative;
}

.milestone-overview::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('/assets/img/bg/pattern.png') repeat;
  opacity: 0.1;
  z-index: 1;
}

.milestone-overview > * {
  position: relative;
  z-index: 2;
}

.milestone-meta {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.milestone-duration-badge {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  padding: 8px 16px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 600;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.milestone-duration-badge i {
  margin-right: 8px;
}

.milestone-number {
  background: rgba(255, 255, 255, 0.9);
  color: #010E5D;
  padding: 8px 16px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.milestone-title {
  font-size: 42px;
  font-weight: 700;
  margin-bottom: 30px;
  line-height: 1.2;
}

.milestone-description .lead {
  font-size: 20px;
  line-height: 1.6;
  opacity: 0.95;
  max-width: 800px;
  margin: 0 auto;
}

/* Section Styles */
.milestone-section {
  padding: 50px 40px;
  border-bottom: 1px solid #e9ecef;
}

.milestone-section:last-child {
  border-bottom: none;
}

.section-title {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 30px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.section-title i {
  color: #010E5D;
  font-size: 24px;
}

/* Deliverables Grid */
.deliverables-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.deliverable-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  border-left: 4px solid #010E5D;
  transition: all 0.3s ease;
}

.deliverable-item:hover {
  background: #e3f2fd;
  transform: translateX(5px);
}

.deliverable-icon {
  width: 40px;
  height: 40px;
  background: #010E5D;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 16px;
  flex-shrink: 0;
}

.deliverable-content p {
  margin: 0;
  color: #495057;
  line-height: 1.6;
  font-weight: 500;
}

/* Technologies Showcase */
.technologies-showcase {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.tech-item {
  display: flex;
  align-items: center;
  gap: 12px;
  background: #fff;
  padding: 15px 20px;
  border-radius: 10px;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
  min-width: 200px;
}

.tech-item:hover {
  border-color: #010E5D;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(1, 14, 93, 0.2);
}

.tech-icon {
  width: 35px;
  height: 35px;
  background: linear-gradient(135deg, #010E5D, #03276e);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 14px;
}

.tech-name {
  font-weight: 600;
  color: #2c3e50;
  font-size: 16px;
}

/* Approach Content */
.approach-content {
  max-width: 800px;
}

.approach-card {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 12px;
  border-left: 5px solid #010E5D;
  position: relative;
}

.approach-card::before {
  content: '"';
  position: absolute;
  top: -10px;
  left: 20px;
  font-size: 60px;
  color: #010E5D;
  opacity: 0.3;
  font-family: serif;
}

.approach-card p {
  font-size: 18px;
  line-height: 1.7;
  color: #495057;
  margin: 0;
  font-style: italic;
}

/* Impact Highlights */
.impact-highlights {
  margin-top: 30px;
}

.impact-stat {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 25px;
  background: #fff;
  border-radius: 12px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  height: 100%;
}

.impact-stat:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border-color: #010E5D;
}

.stat-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #010E5D, #03276e);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 24px;
  flex-shrink: 0;
}

.stat-content h3 {
  font-size: 20px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 8px;
}

.stat-content p {
  color: #6c757d;
  margin: 0;
  line-height: 1.5;
}

/* Navigation Section */
.milestone-navigation {
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
}

.navigation-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
}

.nav-item {
  flex: 1;
}

.nav-item.center {
  text-align: center;
}

.nav-item.next {
  text-align: right;
}

.nav-link {
  display: block;
  padding: 20px;
  background: #fff;
  border-radius: 10px;
  text-decoration: none;
  color: #495057;
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
}

.nav-link:hover {
  background: #007bff;
  color: #fff;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
}

.nav-direction {
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.nav-title {
  font-size: 16px;
  font-weight: 700;
  line-height: 1.3;
}

.back-to-milestones {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  padding: 15px 25px;
  background: #007bff;
  color: #fff;
  text-decoration: none;
  border-radius: 25px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.back-to-milestones:hover {
  background: #0056b3;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 123, 255, 0.4);
}

.nav-placeholder {
  height: 80px;
}

/* CTA Section */
.cta-section {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: #fff;
}

.cta-section .heading1 h2 {
  color: #fff;
}

.cta-section .heading1 p {
  color: rgba(255, 255, 255, 0.9);
}

.cta-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 768px) {
  .milestone-title {
    font-size: 32px;
  }
  
  .milestone-overview {
    padding: 40px 20px;
  }
  
  .milestone-section {
    padding: 30px 20px;
  }
  
  .section-title {
    font-size: 24px;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .deliverables-grid {
    grid-template-columns: 1fr;
  }
  
  .technologies-showcase {
    flex-direction: column;
  }
  
  .tech-item {
    min-width: auto;
  }
  
  .navigation-wrapper {
    flex-direction: column;
    gap: 15px;
  }
  
  .nav-item {
    text-align: center !important;
  }
  
  .impact-stat {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }
  
  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .cta-buttons .theme-btn1,
  .cta-buttons .theme-btn2 {
    width: 100%;
    max-width: 300px;
    text-align: center;
  }
}
