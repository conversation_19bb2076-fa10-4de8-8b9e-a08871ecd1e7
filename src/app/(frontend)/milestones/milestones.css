/* Milestones Page Styles */

.milestones-hero {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
}

.milestones-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('/assets/img/bg/pattern.png') repeat;
  opacity: 0.05;
  z-index: 1;
}

.milestones-hero .container {
  position: relative;
  z-index: 2;
}

/* Timeline Styles */
.milestones-timeline {
  background: #fff;
  position: relative;
}

.timeline-wrapper {
  position: relative;
  padding: 40px 0;
}

.timeline-wrapper::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(to bottom, #010E5D, #03276e);
  transform: translateX(-50%);
  border-radius: 2px;
}

.milestone-item {
  position: relative;
  margin-bottom: 60px;
  display: flex;
  align-items: center;
}

.milestone-item.left {
  justify-content: flex-end;
}

.milestone-item.right {
  justify-content: flex-start;
}

.milestone-content {
  width: 45%;
  position: relative;
}

.milestone-item.left .milestone-content {
  margin-right: 60px;
  text-align: right;
}

.milestone-item.right .milestone-content {
  margin-left: 60px;
  text-align: left;
}

.milestone-card {
  background: #fff;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
  position: relative;
}

.milestone-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.milestone-item.left .milestone-card::after {
  content: '';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 15px solid #fff;
  border-top: 15px solid transparent;
  border-bottom: 15px solid transparent;
}

.milestone-item.right .milestone-card::after {
  content: '';
  position: absolute;
  left: -15px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-right: 15px solid #fff;
  border-top: 15px solid transparent;
  border-bottom: 15px solid transparent;
}

.milestone-header {
  margin-bottom: 20px;
}

.milestone-header h3 {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 8px;
  line-height: 1.3;
}

.milestone-duration {
  display: inline-block;
  background: #010E5D;
  color: #ffffff;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.milestone-description {
  margin-bottom: 20px;
}

.milestone-description p {
  color: #6c757d;
  line-height: 1.6;
  margin-bottom: 0;
}

.milestone-deliverables,
.milestone-approach {
  margin-bottom: 20px;
}

.milestone-deliverables h4,
.milestone-approach h4 {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 12px;
}

.milestone-deliverables ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.milestone-deliverables li {
  position: relative;
  padding-left: 20px;
  margin-bottom: 8px;
  color: #6c757d;
  font-size: 14px;
  line-height: 1.5;
}

.milestone-deliverables li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #28a745;
  font-weight: bold;
}

.milestone-technologies {
  margin-bottom: 20px;
}

.milestone-technologies h4 {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 12px;
}

.tech-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.milestone-item.left .tech-tags {
  justify-content: flex-end;
}

.tech-tag {
  background: #f8f9fa;
  color: #495057;
  padding: 4px 10px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid #dee2e6;
}

.milestone-approach p {
  color: #6c757d;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 0;
}

.milestone-actions {
  margin-top: 20px;
}

.milestone-item.left .milestone-actions {
  text-align: right;
}

.timeline-marker {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
}

.marker-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #010E5D, #03276e);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 18px;
  box-shadow: 0 4px 15px rgba(1, 14, 93, 0.3);
  border: 4px solid #fff;
}

/* Stats Section */
.milestones-stats {
  background: #f8f9fa;
}

.stat-card {
  padding: 30px 20px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  margin-bottom: 30px;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.stat-number {
  font-size: 48px;
  font-weight: 700;
  color: #010E5D;
  margin-bottom: 10px;
  line-height: 1;
}

.stat-label {
  font-size: 16px;
  color: #6c757d;
  font-weight: 500;
}

/* CTA Section */
.cta-section {
  background: linear-gradient(135deg, #010E5D 0%, #03276e 100%);
  color: #ffffff;
}

.cta-section .heading1 h2 {
  color: #ffffff;
}

.cta-section .heading1 p {
  color: rgba(255, 255, 255, 0.9);
}

.cta-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 768px) {
  .timeline-wrapper::before {
    left: 30px;
  }
  
  .milestone-item {
    justify-content: flex-start !important;
  }
  
  .milestone-content {
    width: calc(100% - 80px);
    margin-left: 60px !important;
    margin-right: 0 !important;
    text-align: left !important;
  }
  
  .milestone-card::after {
    display: none;
  }
  
  .timeline-marker {
    left: 30px;
  }
  
  .tech-tags {
    justify-content: flex-start !important;
  }
  
  .milestone-actions {
    text-align: left !important;
  }
  
  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }
  
  .cta-buttons .theme-btn1,
  .cta-buttons .theme-btn2 {
    width: 100%;
    max-width: 300px;
    text-align: center;
  }
}
