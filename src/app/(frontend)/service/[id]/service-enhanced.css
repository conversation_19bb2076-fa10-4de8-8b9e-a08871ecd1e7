/* Enhanced Service Page Styling */

/* ===== GLOBAL SERVICE PAGE STYLES ===== */
.service-page {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
}

/* ===== ENHANCED BANNER SECTION ===== */
.service-banner-enhanced {
  background: linear-gradient(135deg, #010E5D 0%, #03276e 50%, #1a4480 100%);
  position: relative;
  overflow: hidden;
  min-height: 80vh;
  display: flex;
  align-items: center;
}

.service-banner-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="service-grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23service-grid)"/></svg>');
  opacity: 0.3;
  z-index: 1;
}

.service-banner-enhanced .container {
  position: relative;
  z-index: 2;
}

.service-banner-title {
  font-size: 4rem;
  font-weight: 900;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
  margin-bottom: 2rem;
}

.service-banner-description {
  font-size: 1.5rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin-bottom: 3rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.service-banner-cta {
  display: inline-flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 2rem;
  background: linear-gradient(45deg, #e89d1a 0%, #f59e0b 100%);
  color: white;
  text-decoration: none;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 10px 30px rgba(232, 157, 26, 0.3);
}

.service-banner-cta:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(232, 157, 26, 0.4);
  color: white;
  text-decoration: none;
}

/* ===== ENHANCED STATS SECTION ===== */
.service-stats-section {
  background: white;
  padding: 5rem 0;
  position: relative;
  margin-top: -3rem;
  z-index: 3;
  border-radius: 30px 30px 0 0;
  box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.1);
}

.service-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.service-stat-item {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 2.5rem 2rem;
  border-radius: 20px;
  text-align: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(3, 39, 110, 0.1);
  position: relative;
  overflow: hidden;
}

.service-stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #010E5D, #e89d1a, #010E5D);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.service-stat-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.service-stat-item:hover::before {
  transform: scaleX(1);
}

.service-stat-number {
  font-size: 3.5rem;
  font-weight: 900;
  background: linear-gradient(135deg, #010E5D 0%, #03276e 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1;
  margin-bottom: 1rem;
}

.service-stat-label {
  font-size: 1.2rem;
  font-weight: 600;
  color: #334155;
  margin-bottom: 0.5rem;
}

.service-stat-description {
  font-size: 0.9rem;
  color: #64748b;
  line-height: 1.5;
}

/* ===== ENHANCED ABOUT SECTION ===== */
.service-about-enhanced {
  padding: 5rem 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.service-about-content {
  background: white;
  padding: 3rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(3, 39, 110, 0.1);
}

.service-about-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: #1e293b;
  margin-bottom: 2rem;
  position: relative;
}

.service-about-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 60px;
  height: 4px;
  background: linear-gradient(90deg, #010E5D, #e89d1a);
  border-radius: 2px;
}

.service-about-text {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #475569;
  margin-bottom: 2rem;
}

.service-about-image {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.service-about-image:hover {
  transform: scale(1.02);
}

.service-about-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.service-about-image:hover img {
  transform: scale(1.1);
}

/* ===== ENHANCED FEATURES SECTION ===== */
.service-features-enhanced {
  padding: 5rem 0;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
}

.service-features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.service-feature-card {
  background: white;
  padding: 2.5rem;
  border-radius: 20px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(3, 39, 110, 0.1);
  position: relative;
  overflow: hidden;
}

.service-feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #010E5D, #e89d1a, #010E5D);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.service-feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.service-feature-card:hover::before {
  transform: scaleX(1);
}

.service-feature-number {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #010E5D 0%, #03276e 100%);
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  position: relative;
  overflow: hidden;
}

.service-feature-number::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.service-feature-card:hover .service-feature-number::before {
  transform: translateX(100%);
}

.service-feature-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1rem;
}

.service-feature-text {
  font-size: 1rem;
  line-height: 1.6;
  color: #64748b;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .service-banner-title {
    font-size: 2.5rem;
  }
  
  .service-banner-description {
    font-size: 1.2rem;
  }
  
  .service-stats-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .service-features-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .service-about-content {
    padding: 2rem;
  }
  
  .service-feature-card {
    padding: 2rem;
  }
}

@media (max-width: 576px) {
  .service-banner-title {
    font-size: 2rem;
  }
  
  .service-banner-description {
    font-size: 1.1rem;
  }
  
  .service-stat-number {
    font-size: 2.5rem;
  }
  
  .service-about-title {
    font-size: 2rem;
  }
}
