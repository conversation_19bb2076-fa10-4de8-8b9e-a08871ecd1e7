/* Enhanced Projects Page Styling */

/* ===== ENHANCED HERO SECTION ===== */
.projects-hero-content {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
  overflow: hidden;
}

.projects-hero-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e2e8f0" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
  z-index: 1;
}

.projects-hero-content .container {
  position: relative;
  z-index: 2;
}

/* Enhanced Category Tags */
.category-tags {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 12px;
  margin-top: 30px;
}

.category-tag {
  background: linear-gradient(135deg, #010E5D 0%, #03276e 100%);
  color: #ffffff;
  padding: 10px 20px;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(1, 14, 93, 0.2);
  position: relative;
  overflow: hidden;
}

.category-tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.category-tag:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 8px 25px rgba(1, 14, 93, 0.3);
}

.category-tag:hover::before {
  left: 100%;
}

/* ===== ENHANCED STATS SECTION ===== */
.projects-stats-section {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  position: relative;
}

.stats-item {
  background: white;
  border-radius: 20px;
  padding: 40px 30px;
  text-align: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(1, 14, 93, 0.1);
  position: relative;
  overflow: hidden;
}

.stats-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #010E5D, #e89d1a, #010E5D);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.stats-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.stats-item:hover::before {
  transform: scaleX(1);
}

.stats-number {
  margin-bottom: 20px;
  position: relative;
}

.stats-number .counter {
  font-size: 56px;
  font-weight: 800;
  background: linear-gradient(135deg, #010E5D 0%, #03276e 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1;
  display: inline-block;
}

.stats-item h4 {
  font-size: 18px;
  font-weight: 600;
  color: #334155;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* ===== ENHANCED PROJECT CARDS ===== */
.projects-grid-section {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  position: relative;
}

.project-card {
  background: white;
  border-radius: 24px;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(1, 14, 93, 0.05);
  position: relative;
  margin-bottom: 40px;
}

.project-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(1, 14, 93, 0.05) 0%, rgba(232, 157, 26, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.project-card:hover {
  transform: translateY(-15px) scale(1.02);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
}

.project-card:hover::before {
  opacity: 1;
}

.project-image {
  position: relative;
  height: 280px;
  overflow: hidden;
}

.project-image .image {
  height: 100%;
  position: relative;
}

.project-image .image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.project-card:hover .project-image .image img {
  transform: scale(1.15);
}

.project-category {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 2;
}

.category-badge {
  background: linear-gradient(135deg, #010E5D 0%, #03276e 100%);
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(1, 14, 93, 0.3);
}

.project-content {
  padding: 35px;
  position: relative;
  z-index: 2;
}

.project-content h3 {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 16px;
  line-height: 1.3;
}

.project-content h3 a {
  color: #1e293b;
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
}

.project-content h3 a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #010E5D, #e89d1a);
  transition: width 0.3s ease;
}

.project-content h3 a:hover {
  color: #010E5D;
}

.project-content h3 a:hover::after {
  width: 100%;
}

.project-excerpt {
  color: #64748b;
  line-height: 1.7;
  font-size: 16px;
  margin-bottom: 24px;
}

/* Enhanced Project Meta */
.project-meta {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

.meta-item i {
  color: #010E5D;
  width: 16px;
  text-align: center;
}

/* Enhanced Project Tags */
.project-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 30px;
}

.tag-item {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  color: #475569;
  padding: 6px 14px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  transition: all 0.3s ease;
  border: 1px solid rgba(1, 14, 93, 0.1);
}

.tag-item:hover {
  background: linear-gradient(135deg, #010E5D 0%, #03276e 100%);
  color: #ffffff;
  transform: translateY(-2px);
}

.more-tags {
  background: linear-gradient(135deg, #010E5D 0%, #03276e 100%);
  color: #ffffff;
}

/* Enhanced CTA Button */
.theme-btn3 {
  background: linear-gradient(135deg, #010E5D 0%, #03276e 100%);
  color: #ffffff;
  padding: 14px 28px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: inline-flex;
  align-items: center;
  gap: 10px;
  box-shadow: 0 6px 20px rgba(1, 14, 93, 0.3);
  position: relative;
  overflow: hidden;
}

.theme-btn3::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.theme-btn3:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(1, 14, 93, 0.4);
  color: #ffffff;
}

.theme-btn3:hover::before {
  left: 100%;
}

.theme-btn3 span {
  transition: transform 0.3s ease;
}

.theme-btn3:hover span {
  transform: translateX(5px);
}

/* ===== ENHANCED CATEGORY CARDS ===== */
.projects-categories-section {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  position: relative;
}

.category-card {
  background: white;
  border-radius: 24px;
  padding: 40px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(1, 14, 93, 0.05);
  position: relative;
  overflow: hidden;
  margin-bottom: 40px;
}

.category-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(1, 14, 93, 0.02) 0%, rgba(232, 157, 26, 0.02) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.category-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.category-card:hover::before {
  opacity: 1;
}

.category-icon {
  margin-bottom: 25px;
  position: relative;
  z-index: 2;
}

.icon-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 32px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
}

.icon-circle::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.3));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.category-card:hover .icon-circle {
  transform: scale(1.1) rotate(5deg);
}

.category-card:hover .icon-circle::before {
  opacity: 1;
}

.category-content {
  position: relative;
  z-index: 2;
}

.category-content h3 {
  font-size: 22px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 16px;
  line-height: 1.3;
}

.category-content p {
  color: #64748b;
  line-height: 1.7;
  font-size: 16px;
  margin-bottom: 24px;
}

.category-stats {
  display: flex;
  justify-content: center;
  align-items: center;
}

.project-count {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  color: #475569;
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  border: 1px solid rgba(1, 14, 93, 0.1);
  transition: all 0.3s ease;
}

.project-count:hover {
  background: linear-gradient(135deg, #010E5D 0%, #03276e 100%);
  color: #ffffff;
  transform: translateY(-2px);
}

.project-count i {
  color: #010E5D;
  transition: color 0.3s ease;
}

.project-count:hover i {
  color: #ffffff;
}

/* ===== ENHANCED CTA SECTION ===== */
.cta-section {
  background: linear-gradient(135deg, #010E5D 0%, #03276e 100%);
  position: relative;
  overflow: hidden;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
  z-index: 1;
}

.cta-section .container {
  position: relative;
  z-index: 2;
}

.cta-section .heading1 h2 {
  color: #ffffff;
  font-size: 42px;
  font-weight: 800;
  margin-bottom: 20px;
}

.cta-section .heading1 p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 18px;
  line-height: 1.7;
}

.cta-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.theme-btn1, .theme-btn2 {
  padding: 16px 32px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: inline-flex;
  align-items: center;
  gap: 10px;
  position: relative;
  overflow: hidden;
}

.theme-btn1 {
  background: linear-gradient(135deg, #e89d1a 0%, #f59e0b 100%);
  color: white;
  box-shadow: 0 6px 20px rgba(232, 157, 26, 0.3);
}

.theme-btn2 {
  background: transparent;
  color: #ffffff;
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 6px 20px rgba(255, 255, 255, 0.1);
}

.theme-btn1:hover, .theme-btn2:hover {
  transform: translateY(-3px);
}

.theme-btn1:hover {
  box-shadow: 0 10px 30px rgba(232, 157, 26, 0.4);
  color: #ffffff;
}

.theme-btn2:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.5);
  box-shadow: 0 10px 30px rgba(255, 255, 255, 0.2);
  color: #ffffff;
}

/* ===== NO PROJECTS MESSAGE ===== */
.no-projects-message {
  background: white;
  border-radius: 24px;
  padding: 80px 40px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(1, 14, 93, 0.05);
}

.no-projects-message h3 {
  color: #1e293b;
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 16px;
}

.no-projects-message p {
  color: #64748b;
  font-size: 18px;
  line-height: 1.7;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
  .stats-number .counter {
    font-size: 48px;
  }

  .project-content {
    padding: 30px;
  }

  .category-card {
    padding: 35px;
  }
}

@media (max-width: 768px) {
  .category-tags {
    justify-content: center;
  }

  .category-tag {
    font-size: 12px;
    padding: 8px 16px;
  }

  .stats-number .counter {
    font-size: 40px;
  }

  .stats-item {
    padding: 30px 20px;
    margin-bottom: 20px;
  }

  .project-card {
    margin-bottom: 30px;
  }

  .project-content {
    padding: 25px;
  }

  .project-content h3 {
    font-size: 20px;
  }

  .category-card {
    padding: 30px;
    margin-bottom: 30px;
  }

  .icon-circle {
    width: 70px;
    height: 70px;
    font-size: 28px;
  }

  .category-content h3 {
    font-size: 20px;
  }

  .cta-section .heading1 h2 {
    font-size: 32px;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .theme-btn1, .theme-btn2 {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .project-image {
    height: 220px;
  }

  .project-content {
    padding: 20px;
  }

  .category-card {
    padding: 25px;
  }

  .no-projects-message {
    padding: 60px 30px;
  }

  .cta-section .heading1 h2 {
    font-size: 28px;
  }
}

/* ===== ANIMATION EFFECTS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Animation Classes */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out forwards;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.6s ease-out forwards;
}

.animate-fade-in-right {
  animation: fadeInRight 0.6s ease-out forwards;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

/* Staggered Animation Delays */
.project-card:nth-child(1) { animation-delay: 0.1s; }
.project-card:nth-child(2) { animation-delay: 0.2s; }
.project-card:nth-child(3) { animation-delay: 0.3s; }
.project-card:nth-child(4) { animation-delay: 0.4s; }
.project-card:nth-child(5) { animation-delay: 0.5s; }
.project-card:nth-child(6) { animation-delay: 0.6s; }

.category-card:nth-child(1) { animation-delay: 0.1s; }
.category-card:nth-child(2) { animation-delay: 0.2s; }
.category-card:nth-child(3) { animation-delay: 0.3s; }
.category-card:nth-child(4) { animation-delay: 0.4s; }

.stats-item:nth-child(1) { animation-delay: 0.1s; }
.stats-item:nth-child(2) { animation-delay: 0.2s; }
.stats-item:nth-child(3) { animation-delay: 0.3s; }
.stats-item:nth-child(4) { animation-delay: 0.4s; }

/* ===== LOADING STATES ===== */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

.project-card-skeleton {
  height: 400px;
  border-radius: 24px;
  margin-bottom: 40px;
}

.stats-skeleton {
  height: 120px;
  border-radius: 20px;
  margin-bottom: 30px;
}

.category-skeleton {
  height: 200px;
  border-radius: 24px;
  margin-bottom: 40px;
}

/* ===== ENHANCED INTERACTIONS ===== */
.project-card {
  cursor: pointer;
}

.project-card:active {
  transform: translateY(-12px) scale(1.01);
}

.category-card:active {
  transform: translateY(-8px) scale(1.01);
}

.stats-item:active {
  transform: translateY(-8px) scale(1.01);
}

/* Enhanced Focus States for Accessibility */
.project-card:focus-within,
.category-card:focus-within,
.stats-item:focus-within {
  outline: 3px solid rgba(1, 14, 93, 0.3);
  outline-offset: 2px;
}

.theme-btn3:focus,
.theme-btn1:focus,
.theme-btn2:focus {
  outline: 3px solid rgba(232, 157, 26, 0.3);
  outline-offset: 2px;
}

/* ===== PRINT STYLES ===== */
@media print {
  .projects-hero-content,
  .projects-stats-section,
  .projects-grid-section,
  .projects-categories-section {
    background: white !important;
    box-shadow: none !important;
  }

  .project-card,
  .category-card,
  .stats-item {
    break-inside: avoid;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  }

  .theme-btn3,
  .theme-btn1,
  .theme-btn2 {
    background: #010E5D !important;
    color: #ffffff !important;
  }
}
