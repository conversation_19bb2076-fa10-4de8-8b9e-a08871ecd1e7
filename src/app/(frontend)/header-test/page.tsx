import Layout from "@/components/layout/Layout";
import HeaderTest from "@/components/layout/header/HeaderTest";

export default function HeaderTestPage() {
  return (
    <Layout>
      <div style={{ paddingTop: '100px', minHeight: '200vh' }}>
        <div className="container">
          <div className="row">
            <div className="col-12">
              <div className="text-center py-5">
                <h1 className="mb-4">New Modern Header Test Page</h1>
                <p className="lead mb-4">
                  This page is designed to test the new header functionality.
                </p>
                
                <div className="row mt-5">
                  <div className="col-md-4 mb-4">
                    <div className="card h-100">
                      <div className="card-body">
                        <h5 className="card-title">
                          <i className="fa-solid fa-mobile-alt me-2 text-primary"></i>
                          Mobile Responsiveness
                        </h5>
                        <p className="card-text">
                          Test the hamburger menu and mobile navigation on different screen sizes.
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="col-md-4 mb-4">
                    <div className="card h-100">
                      <div className="card-body">
                        <h5 className="card-title">
                          <i className="fa-solid fa-mouse-pointer me-2 text-primary"></i>
                          Dropdown Menus
                        </h5>
                        <p className="card-text">
                          Hover over "Services" and "Projects" in the navigation to test dropdown functionality.
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="col-md-4 mb-4">
                    <div className="card h-100">
                      <div className="card-body">
                        <h5 className="card-title">
                          <i className="fa-solid fa-arrows-alt-v me-2 text-primary"></i>
                          Sticky Navigation
                        </h5>
                        <p className="card-text">
                          Scroll down to see the header stick to the top with smooth transitions.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-5">
                  <h3>Test Instructions</h3>
                  <div className="row">
                    <div className="col-md-6">
                      <h5>Desktop Testing:</h5>
                      <ul className="text-start">
                        <li>Hover over navigation items to see hover effects</li>
                        <li>Click on "Services" and "Projects" to test dropdowns</li>
                        <li>Scroll down to test sticky header behavior</li>
                        <li>Use Tab key to test keyboard navigation</li>
                      </ul>
                    </div>
                    <div className="col-md-6">
                      <h5>Mobile Testing:</h5>
                      <ul className="text-start">
                        <li>Resize browser window to mobile size</li>
                        <li>Click hamburger menu to open mobile navigation</li>
                        <li>Test dropdown toggles in mobile menu</li>
                        <li>Test overlay click to close menu</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div className="mt-5 p-4 bg-light rounded">
                  <h4>Scroll Test Area</h4>
                  <p>Keep scrolling to test the sticky header functionality...</p>
                  
                  {/* Generate content to enable scrolling */}
                  {Array.from({ length: 20 }, (_, i) => (
                    <div key={i} className="mb-4 p-3 border rounded">
                      <h5>Test Section {i + 1}</h5>
                      <p>
                        This is test content section {i + 1}. The header should remain sticky 
                        at the top as you scroll through this content. Lorem ipsum dolor sit amet, 
                        consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore 
                        et dolore magna aliqua.
                      </p>
                    </div>
                  ))}
                </div>

                <div className="mt-5 p-4 bg-primary text-white rounded">
                  <h4>Accessibility Features</h4>
                  <ul className="text-start">
                    <li>Proper ARIA labels and roles</li>
                    <li>Keyboard navigation support</li>
                    <li>Focus management</li>
                    <li>Screen reader compatibility</li>
                    <li>High contrast mode support</li>
                    <li>Reduced motion support</li>
                  </ul>
                </div>

                <div className="mt-5">
                  <h4>Performance Features</h4>
                  <div className="row">
                    <div className="col-md-6">
                      <div className="card">
                        <div className="card-body">
                          <h6 className="card-title">Optimizations</h6>
                          <ul className="text-start small">
                            <li>Hardware acceleration</li>
                            <li>Passive scroll listeners</li>
                            <li>Efficient state management</li>
                            <li>Smooth animations</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                    <div className="col-md-6">
                      <div className="card">
                        <div className="card-body">
                          <h6 className="card-title">Browser Support</h6>
                          <ul className="text-start small">
                            <li>Modern browsers</li>
                            <li>Mobile browsers</li>
                            <li>Progressive enhancement</li>
                            <li>Graceful degradation</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Include the test component */}
      <HeaderTest />
    </Layout>
  );
}
