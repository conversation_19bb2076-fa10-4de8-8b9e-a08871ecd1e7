import Link from 'next/link'
import { Home, Search, ArrowLeft } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import Layout from '@/components/layout/Layout'
import SectionHeader from '@/components/layout/SectionHeader'

export default function BlogPostNotFound() {
  return (
    <Layout>
      <SectionHeader
        title="Blog Post Not Found"
        group_page="Blog"
        current_page="404"
        display="d-block"
      />
      
      <section className="py-16 lg:py-24">
        <div className="container mx-auto px-4">
          <div className="min-h-[50vh] flex items-center justify-center">
            <Card className="w-full max-w-lg text-center">
              <CardHeader>
                <div className="mx-auto mb-6 text-8xl font-bold text-muted-foreground/50">
                  404
                </div>
                <CardTitle className="text-2xl lg:text-3xl">
                  Blog Post Not Found
                </CardTitle>
                <CardDescription className="text-base">
                  The blog post you're looking for doesn't exist, may have been moved, or is no longer available.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="text-sm text-muted-foreground">
                  <p>This could happen if:</p>
                  <ul className="mt-2 space-y-1 text-left max-w-sm mx-auto">
                    <li>• The URL was typed incorrectly</li>
                    <li>• The post was unpublished or deleted</li>
                    <li>• The link you followed is outdated</li>
                  </ul>
                </div>
                
                <div className="flex flex-col gap-3 sm:flex-row sm:justify-center">
                  <Button asChild className="flex-1 sm:flex-none">
                    <Link href="/blog">
                      <ArrowLeft className="mr-2 h-4 w-4" />
                      Back to Blog
                    </Link>
                  </Button>
                  <Button variant="outline" asChild className="flex-1 sm:flex-none">
                    <Link href="/blog?search=">
                      <Search className="mr-2 h-4 w-4" />
                      Search Posts
                    </Link>
                  </Button>
                  <Button variant="outline" asChild className="flex-1 sm:flex-none">
                    <Link href="/">
                      <Home className="mr-2 h-4 w-4" />
                      Go Home
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </Layout>
  )
}
