import { render, screen, waitFor } from '@testing-library/react'
import { BlogService } from '@/lib/services/blog.service'
import { BlogPostDetailComponent } from '@/components/blog/blog-post-detail'
import { PostStatus } from '@prisma/client'

// Mock the BlogService
jest.mock('@/lib/services/blog.service')
const mockBlogService = BlogService as jest.Mocked<typeof BlogService>

// Mock Next.js components
jest.mock('next/link', () => {
  return ({ children, href }: { children: React.ReactNode; href: string }) => (
    <a href={href}>{children}</a>
  )
})

jest.mock('next/navigation', () => ({
  notFound: jest.fn(),
}))

const mockBlogPost = {
  id: 'test-post-1',
  title: 'Test Blog Post',
  slug: 'test-blog-post',
  excerpt: 'This is a test blog post excerpt',
  content: '<h2>Test Content</h2><p>This is test content with <strong>bold text</strong> and <em>italic text</em>.</p>',
  featuredImage: '/test-image.jpg',
  status: PostStatus.PUBLISHED,
  publishedAt: new Date('2024-01-15T10:00:00Z'),
  createdAt: new Date('2024-01-15T09:00:00Z'),
  updatedAt: new Date('2024-01-15T10:00:00Z'),
  seoTitle: 'Test Blog Post SEO Title',
  seoDescription: 'Test blog post SEO description',
  seoKeywords: ['test', 'blog', 'post'],
  authorId: 'test-author-1',
  categoryId: 'test-category-1',
  author: {
    id: 'test-author-1',
    name: 'Test Author',
    email: '<EMAIL>',
    image: '/test-author.jpg',
    role: 'USER' as any,
  },
  category: {
    id: 'test-category-1',
    name: 'Test Category',
    slug: 'test-category',
    color: '#3b82f6',
    description: 'Test category description',
  },
  tags: [
    {
      id: 'test-tag-1',
      name: 'Test Tag',
      slug: 'test-tag',
      color: '#ef4444',
    },
  ],
  comments: [],
  blocks: [],
  _count: {
    comments: 0,
    tags: 1,
  },
  relatedPosts: [],
}

describe('Blog Details Page', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders blog post content correctly', async () => {
    render(<BlogPostDetailComponent post={mockBlogPost} />)

    // Check if title is rendered
    expect(screen.getByText('Test Blog Post')).toBeInTheDocument()

    // Check if excerpt is rendered
    expect(screen.getByText('This is a test blog post excerpt')).toBeInTheDocument()

    // Check if author name is rendered
    expect(screen.getByText('By Test Author')).toBeInTheDocument()

    // Check if category is rendered
    expect(screen.getByText('Test Category')).toBeInTheDocument()

    // Check if tag is rendered
    expect(screen.getByText('Test Tag')).toBeInTheDocument()

    // Check if content is rendered (HTML content should be processed)
    expect(screen.getByText('Test Content')).toBeInTheDocument()
    expect(screen.getByText('This is test content with')).toBeInTheDocument()
  })

  it('renders featured image when provided', () => {
    render(<BlogPostDetailComponent post={mockBlogPost} />)

    const featuredImage = screen.getByAltText('Test Blog Post')
    expect(featuredImage).toBeInTheDocument()
    expect(featuredImage).toHaveAttribute('src', '/test-image.jpg')
  })

  it('renders back to blog link', () => {
    render(<BlogPostDetailComponent post={mockBlogPost} />)

    const backLink = screen.getByText('Back to Blog')
    expect(backLink).toBeInTheDocument()
    expect(backLink.closest('a')).toHaveAttribute('href', '/blog')
  })

  it('renders author bio section', () => {
    render(<BlogPostDetailComponent post={mockBlogPost} />)

    expect(screen.getByText('About Test Author')).toBeInTheDocument()
  })

  it('renders social share components', () => {
    render(<BlogPostDetailComponent post={mockBlogPost} />)

    // Should render share section
    expect(screen.getByText('Share this article')).toBeInTheDocument()
  })

  it('handles missing optional fields gracefully', () => {
    const postWithoutOptionalFields = {
      ...mockBlogPost,
      excerpt: undefined,
      featuredImage: undefined,
      category: undefined,
      tags: [],
    }

    render(<BlogPostDetailComponent post={postWithoutOptionalFields} />)

    // Should still render title and content
    expect(screen.getByText('Test Blog Post')).toBeInTheDocument()
    expect(screen.getByText('Test Content')).toBeInTheDocument()

    // Should not crash when optional fields are missing
    expect(screen.queryByText('This is a test blog post excerpt')).not.toBeInTheDocument()
  })

  it('renders related posts when provided', () => {
    const postWithRelatedPosts = {
      ...mockBlogPost,
      relatedPosts: [
        {
          id: 'related-1',
          title: 'Related Post 1',
          slug: 'related-post-1',
          excerpt: 'Related post excerpt',
          featuredImage: '/related-1.jpg',
          publishedAt: new Date('2024-01-10T10:00:00Z'),
          author: { name: 'Related Author', image: null },
          category: { name: 'Related Category', slug: 'related-category', color: '#10b981' },
          tags: [],
        },
      ],
    }

    render(<BlogPostDetailComponent post={postWithRelatedPosts} />)

    expect(screen.getByText('Related Articles')).toBeInTheDocument()
    expect(screen.getByText('Related Post 1')).toBeInTheDocument()
  })

  it('renders structured data scripts', () => {
    render(<BlogPostDetailComponent post={mockBlogPost} />)

    // Check if structured data scripts are present
    const scripts = document.querySelectorAll('script[type="application/ld+json"]')
    expect(scripts.length).toBeGreaterThan(0)

    // Check if the structured data contains expected content
    const structuredDataScript = Array.from(scripts).find(script => 
      script.textContent?.includes('BlogPosting')
    )
    expect(structuredDataScript).toBeTruthy()

    if (structuredDataScript) {
      const structuredData = JSON.parse(structuredDataScript.textContent || '{}')
      expect(structuredData['@type']).toBe('BlogPosting')
      expect(structuredData.headline).toBe('Test Blog Post')
    }
  })

  it('handles content rendering with TipTap renderer', () => {
    render(<BlogPostDetailComponent post={mockBlogPost} />)

    // The content should be rendered with proper HTML structure
    const contentContainer = document.querySelector('.prose')
    expect(contentContainer).toBeInTheDocument()

    // Check if HTML content is properly rendered
    const heading = screen.getByRole('heading', { name: 'Test Content' })
    expect(heading).toBeInTheDocument()
    expect(heading.tagName).toBe('H2')
  })
})

describe('Blog Details Page Error Handling', () => {
  it('shows loading skeleton while content is loading', () => {
    // This would be tested in integration tests with Suspense
    expect(true).toBe(true) // Placeholder for loading state test
  })

  it('shows 404 page for non-existent posts', () => {
    // This would be tested in integration tests
    expect(true).toBe(true) // Placeholder for 404 test
  })
})
