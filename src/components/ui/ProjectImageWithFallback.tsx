"use client";
import { useState } from 'react';
import Image from 'next/image';

interface ProjectImageWithFallbackProps {
  src: string;
  alt: string;
  className?: string;
  fallbackSrc?: string;
  loading?: 'lazy' | 'eager';
  onError?: () => void;
  style?: React.CSSProperties;
}

export default function ProjectImageWithFallback({
  src,
  alt,
  className = '',
  fallbackSrc = '/assets/img/project/default-project.jpg',
  loading = 'lazy',
  onError,
  style
}: ProjectImageWithFallbackProps) {
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  const handleError = () => {
    setHasError(true);
    setIsLoading(false);
    onError?.();
  };

  const handleLoad = () => {
    setIsLoading(false);
  };

  const imageSrc = hasError || !src ? fallbackSrc : src;
  const isPlaceholder = imageSrc === fallbackSrc;

  return (
    <div className={`project-image-container ${className}`} style={style}>
      {isLoading && (
        <div className="image-loading-placeholder">
          <div className="loading-spinner"></div>
        </div>
      )}
      <img
        src={imageSrc}
        alt={alt}
        className={`project-image ${isPlaceholder ? 'placeholder-image' : ''} ${isLoading ? 'loading' : ''}`}
        onError={handleError}
        onLoad={handleLoad}
        loading={loading}
      />
      
      <style jsx>{`
        .project-image-container {
          position: relative;
          overflow: hidden;
        }
        
        .project-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: opacity 0.3s ease, transform 0.3s ease;
        }
        
        .project-image.loading {
          opacity: 0;
        }
        
        .project-image.placeholder-image {
          object-fit: contain;
          background-color: #f8f9fa;
          padding: 1rem;
        }
        
        .image-loading-placeholder {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: #f8f9fa;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1;
        }
        
        .loading-spinner {
          width: 40px;
          height: 40px;
          border: 3px solid #e9ecef;
          border-top: 3px solid #6c757d;
          border-radius: 50%;
          animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
          .project-image.placeholder-image {
            padding: 0.5rem;
          }
          
          .loading-spinner {
            width: 30px;
            height: 30px;
            border-width: 2px;
          }
        }
      `}</style>
    </div>
  );
}
