import { BlogPostDetail } from '@/types/blog'

interface BlogStructuredDataProps {
  post: BlogPostDetail
}

export function BlogStructuredData({ post }: BlogStructuredDataProps) {
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'
  
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'BlogPosting',
    headline: post.title,
    description: post.excerpt || post.seoDescription,
    image: post.featuredImage ? [
      post.featuredImage.startsWith('http') 
        ? post.featuredImage 
        : `${siteUrl}${post.featuredImage}`
    ] : [`${siteUrl}/assets/img/logo/logo.png`],
    datePublished: post.publishedAt ? new Date(post.publishedAt).toISOString() : undefined,
    dateModified: post.updatedAt ? new Date(post.updatedAt).toISOString() : undefined,
    author: {
      '@type': 'Person',
      name: post.author.name || 'Motshwanelo IT Consulting',
      image: post.author.image ? (
        post.author.image.startsWith('http') 
          ? post.author.image 
          : `${siteUrl}${post.author.image}`
      ) : undefined,
    },
    publisher: {
      '@type': 'Organization',
      name: 'Motshwanelo IT Consulting',
      logo: {
        '@type': 'ImageObject',
        url: `${siteUrl}/assets/img/logo/logo.png`,
        width: 200,
        height: 60,
      },
    },
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': `${siteUrl}/blog/${post.slug}`,
    },
    url: `${siteUrl}/blog/${post.slug}`,
    wordCount: post.content ? post.content.replace(/<[^>]*>/g, '').split(/\s+/).length : undefined,
    timeRequired: post.readTime ? `PT${post.readTime}M` : undefined,
    keywords: [
      ...(post.category ? [post.category.name] : []),
      ...(post.tags?.map(tag => tag.name) || [])
    ].join(', '),
    articleSection: post.category?.name,
    about: post.category ? {
      '@type': 'Thing',
      name: post.category.name,
      description: post.category.description,
    } : undefined,
    inLanguage: 'en-US',
    isAccessibleForFree: true,
    copyrightHolder: {
      '@type': 'Organization',
      name: 'Motshwanelo IT Consulting',
    },
    copyrightYear: post.publishedAt ? new Date(post.publishedAt).getFullYear() : new Date().getFullYear(),
    license: `${siteUrl}/terms-of-service`,
  }

  // Remove undefined values
  const cleanStructuredData = JSON.parse(JSON.stringify(structuredData))

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(cleanStructuredData, null, 2),
      }}
    />
  )
}

// Breadcrumb structured data
interface BlogBreadcrumbProps {
  post: BlogPostDetail
}

export function BlogBreadcrumbStructuredData({ post }: BlogBreadcrumbProps) {
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'
  
  const breadcrumbData = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: [
      {
        '@type': 'ListItem',
        position: 1,
        name: 'Home',
        item: siteUrl,
      },
      {
        '@type': 'ListItem',
        position: 2,
        name: 'Blog',
        item: `${siteUrl}/blog`,
      },
      ...(post.category ? [{
        '@type': 'ListItem',
        position: 3,
        name: post.category.name,
        item: `${siteUrl}/blog/category/${post.category.slug}`,
      }] : []),
      {
        '@type': 'ListItem',
        position: post.category ? 4 : 3,
        name: post.title,
        item: `${siteUrl}/blog/${post.slug}`,
      },
    ],
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(breadcrumbData, null, 2),
      }}
    />
  )
}

// Organization structured data
export function OrganizationStructuredData() {
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'
  
  const organizationData = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'Motshwanelo IT Consulting',
    alternateName: 'MITC',
    url: siteUrl,
    logo: `${siteUrl}/assets/img/logo/logo.png`,
    description: 'Professional IT consulting services specializing in technology solutions, digital transformation, and business optimization.',
    foundingDate: '2020',
    address: {
      '@type': 'PostalAddress',
      addressCountry: 'ZA',
      addressRegion: 'Gauteng',
      addressLocality: 'Johannesburg',
    },
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '+27-11-123-4567',
      contactType: 'customer service',
      availableLanguage: ['English'],
    },
    sameAs: [
      'https://www.linkedin.com/company/motshwanelo-it-consulting',
      'https://twitter.com/motshwaneloitc',
      'https://www.facebook.com/motshwaneloitc',
    ],
    knowsAbout: [
      'IT Consulting',
      'Digital Transformation',
      'Cloud Solutions',
      'Cybersecurity',
      'Software Development',
      'Technology Strategy',
    ],
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(organizationData, null, 2),
      }}
    />
  )
}
