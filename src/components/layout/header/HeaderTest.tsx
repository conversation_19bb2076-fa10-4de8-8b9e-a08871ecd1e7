"use client";

import React, { useEffect, useState } from 'react';

interface TestResult {
  test: string;
  passed: boolean;
  message: string;
}

const HeaderTest: React.FC = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const runTests = async () => {
    setIsRunning(true);
    const results: TestResult[] = [];

    // Test 1: Header Element Exists
    const headerExists = document.querySelector('.new-modern-header') !== null;
    results.push({
      test: 'Header Element Exists',
      passed: headerExists,
      message: headerExists ? 'Header element found' : 'Header element not found'
    });

    // Test 2: Mobile Menu Toggle
    const mobileToggle = document.querySelector('.navbar-toggler');
    const mobileToggleExists = mobileToggle !== null;
    results.push({
      test: 'Mobile Menu Toggle',
      passed: mobileToggleExists,
      message: mobileToggleExists ? 'Mobile toggle button found' : 'Mobile toggle button not found'
    });

    // Test 3: Dropdown Menus
    const dropdowns = document.querySelectorAll('.dropdown-menu');
    const dropdownsExist = dropdowns.length >= 2;
    results.push({
      test: 'Dropdown Menus',
      passed: dropdownsExist,
      message: dropdownsExist ? `Found ${dropdowns.length} dropdown menus` : 'Dropdown menus not found'
    });

    // Test 4: Accessibility - ARIA Labels
    const ariaElements = document.querySelectorAll('[aria-label], [aria-expanded], [role]');
    const hasAria = ariaElements.length > 0;
    results.push({
      test: 'Accessibility - ARIA Labels',
      passed: hasAria,
      message: hasAria ? `Found ${ariaElements.length} elements with ARIA attributes` : 'No ARIA attributes found'
    });

    // Test 5: Logo Image
    const logo = document.querySelector('.header-logo');
    const logoExists = logo !== null;
    results.push({
      test: 'Logo Image',
      passed: logoExists,
      message: logoExists ? 'Logo image found' : 'Logo image not found'
    });

    // Test 6: Navigation Links
    const navLinks = document.querySelectorAll('.nav-link');
    const hasNavLinks = navLinks.length >= 5;
    results.push({
      test: 'Navigation Links',
      passed: hasNavLinks,
      message: hasNavLinks ? `Found ${navLinks.length} navigation links` : 'Insufficient navigation links'
    });

    // Test 7: Responsive Behavior
    const isMobile = window.innerWidth < 992;
    const mobileMenu = document.querySelector('.mobile-menu');
    const desktopNav = document.querySelector('.navbar-collapse');
    
    let responsiveTest = false;
    let responsiveMessage = '';
    
    if (isMobile) {
      responsiveTest = mobileMenu !== null;
      responsiveMessage = responsiveTest ? 'Mobile menu found on mobile screen' : 'Mobile menu not found on mobile screen';
    } else {
      responsiveTest = desktopNav !== null;
      responsiveMessage = responsiveTest ? 'Desktop navigation found on desktop screen' : 'Desktop navigation not found on desktop screen';
    }
    
    results.push({
      test: 'Responsive Behavior',
      passed: responsiveTest,
      message: responsiveMessage
    });

    // Test 8: Sticky Header Functionality
    const header = document.querySelector('.new-modern-header');
    const isSticky = header?.classList.contains('sticky') || false;
    const scrollY = window.scrollY;
    
    results.push({
      test: 'Sticky Header',
      passed: true, // This test always passes as we can't simulate scroll in test
      message: `Header sticky state: ${isSticky}, Scroll position: ${scrollY}px`
    });

    // Test 9: CSS Loaded
    const headerStyles = window.getComputedStyle(document.querySelector('.new-modern-header') as Element);
    const cssLoaded = headerStyles.position !== 'static';
    results.push({
      test: 'CSS Styles Loaded',
      passed: cssLoaded,
      message: cssLoaded ? 'Header CSS styles are applied' : 'Header CSS styles not loaded'
    });

    // Test 10: Focus Management
    const focusableElements = document.querySelectorAll(
      '.nav-link, .dropdown-item, .navbar-toggler, .mobile-nav-link'
    );
    const hasFocusableElements = focusableElements.length > 0;
    results.push({
      test: 'Focus Management',
      passed: hasFocusableElements,
      message: hasFocusableElements ? `Found ${focusableElements.length} focusable elements` : 'No focusable elements found'
    });

    setTestResults(results);
    setIsRunning(false);
  };

  useEffect(() => {
    // Auto-run tests after component mounts
    const timer = setTimeout(() => {
      runTests();
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const passedTests = testResults.filter(r => r.passed).length;
  const totalTests = testResults.length;
  const passRate = totalTests > 0 ? (passedTests / totalTests) * 100 : 0;

  return (
    <div style={{
      position: 'fixed',
      bottom: '20px',
      right: '20px',
      background: 'white',
      border: '1px solid #ddd',
      borderRadius: '8px',
      padding: '16px',
      maxWidth: '400px',
      maxHeight: '500px',
      overflow: 'auto',
      boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
      zIndex: 10000,
      fontSize: '14px'
    }}>
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '12px' }}>
        <h4 style={{ margin: 0, fontSize: '16px', fontWeight: 'bold' }}>🧪 Header Tests</h4>
        <button
          onClick={runTests}
          disabled={isRunning}
          style={{
            background: '#ff7a01',
            color: 'white',
            border: 'none',
            padding: '4px 8px',
            borderRadius: '4px',
            fontSize: '12px',
            cursor: isRunning ? 'not-allowed' : 'pointer'
          }}
        >
          {isRunning ? 'Running...' : 'Run Tests'}
        </button>
      </div>

      {totalTests > 0 && (
        <div style={{ 
          marginBottom: '12px', 
          padding: '8px', 
          background: passRate === 100 ? '#d4edda' : passRate >= 80 ? '#fff3cd' : '#f8d7da',
          borderRadius: '4px',
          fontSize: '12px'
        }}>
          <strong>Results: {passedTests}/{totalTests} tests passed ({passRate.toFixed(1)}%)</strong>
        </div>
      )}

      <div style={{ maxHeight: '300px', overflow: 'auto' }}>
        {testResults.map((result, index) => (
          <div
            key={index}
            style={{
              display: 'flex',
              alignItems: 'flex-start',
              marginBottom: '8px',
              padding: '8px',
              background: result.passed ? '#f8f9fa' : '#fff5f5',
              borderLeft: `3px solid ${result.passed ? '#28a745' : '#dc3545'}`,
              borderRadius: '4px'
            }}
          >
            <span style={{ marginRight: '8px', fontSize: '16px' }}>
              {result.passed ? '✅' : '❌'}
            </span>
            <div style={{ flex: 1 }}>
              <div style={{ fontWeight: 'bold', marginBottom: '2px' }}>
                {result.test}
              </div>
              <div style={{ fontSize: '12px', color: '#666' }}>
                {result.message}
              </div>
            </div>
          </div>
        ))}
      </div>

      {totalTests === 0 && !isRunning && (
        <div style={{ textAlign: 'center', color: '#666', padding: '20px' }}>
          Click "Run Tests" to start testing the header component
        </div>
      )}
    </div>
  );
};

export default HeaderTest;
