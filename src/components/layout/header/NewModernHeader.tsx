"use client";

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

interface HeaderProps {
  className?: string;
}

const NewModernHeader: React.FC<HeaderProps> = ({ className = '' }) => {
  const [isSticky, setIsSticky] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);
  const [mobileDropdowns, setMobileDropdowns] = useState<{[key: string]: boolean}>({});
  const pathname = usePathname();
  const dropdownTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Handle scroll for sticky header
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      const shouldBeSticky = scrollTop > 100;

      if (shouldBeSticky !== isSticky) {
        setIsSticky(shouldBeSticky);

        // Add/remove body class for sticky header
        if (shouldBeSticky) {
          document.body.classList.add('header-sticky');
        } else {
          document.body.classList.remove('header-sticky');
        }
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll(); // Check initial position

    return () => {
      window.removeEventListener('scroll', handleScroll);
      document.body.classList.remove('header-sticky');
    };
  }, [isSticky]);

  // Handle mobile menu body scroll
  useEffect(() => {
    if (isMobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isMobileMenuOpen]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setIsMobileMenuOpen(false);
        setActiveDropdown(null);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Close mobile menu on route change
  useEffect(() => {
    setIsMobileMenuOpen(false);
    setActiveDropdown(null);
    setMobileDropdowns({});
  }, [pathname]);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const toggleMobileDropdown = (dropdown: string) => {
    setMobileDropdowns(prev => ({
      ...prev,
      [dropdown]: !prev[dropdown]
    }));
  };

  const handleDropdownEnter = (dropdown: string) => {
    if (dropdownTimeoutRef.current) {
      clearTimeout(dropdownTimeoutRef.current);
    }
    setActiveDropdown(dropdown);
  };

  const handleDropdownLeave = () => {
    dropdownTimeoutRef.current = setTimeout(() => {
      setActiveDropdown(null);
    }, 150);
  };

  const handleDropdownKeyDown = (e: React.KeyboardEvent, dropdown: string) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      setActiveDropdown(activeDropdown === dropdown ? null : dropdown);
    } else if (e.key === 'Escape') {
      setActiveDropdown(null);
    }
  };

  const handleDropdownItemKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setActiveDropdown(null);
      // Focus back to the dropdown trigger
      const trigger = document.querySelector(`[aria-expanded="true"]`) as HTMLElement;
      if (trigger) trigger.focus();
    }
  };

  const isActive = (path: string) => {
    if (path === '/' && pathname === '/') return true;
    if (path !== '/' && pathname.startsWith(path)) return true;
    return false;
  };

  const servicesItems = [
    { href: '/service/data-centre', label: 'Data Centre Solutions', icon: 'fa-server' },
    { href: '/service/smart-city', label: 'Smart City Solutions', icon: 'fa-city' },
    { href: '/service/it-consulting', label: 'IT Consulting', icon: 'fa-lightbulb' },
    { href: '/service/software-development', label: 'Software Development', icon: 'fa-code' },
  ];

  const projectsItems = [
    { href: '/success-stories', label: 'Success Stories', icon: 'fa-trophy' },
    { href: '/milestones', label: 'Milestones', icon: 'fa-flag-checkered' },
  ];

  return (
    <>
      {/* Main Header */}
      <header className={`new-modern-header ${isSticky ? 'sticky' : ''} ${className}`}>
        <div className="container">
          <div className="row">
            <div className="col-12">
              <nav className="navbar navbar-expand-lg">
                {/* Logo */}
                <Link href="/" className="navbar-brand">
                  <img
                    src="/assets/img/logo/logo_full.svg"
                    alt="Motshwanelo IT Consulting"
                    className="header-logo"
                  />
                </Link>

                {/* Mobile Menu Toggle */}
                <button
                  className="navbar-toggler d-lg-none"
                  type="button"
                  onClick={toggleMobileMenu}
                  aria-controls="navbarNav"
                  aria-expanded={isMobileMenuOpen}
                  aria-label="Toggle navigation"
                >
                  <span className={`hamburger-icon ${isMobileMenuOpen ? 'active' : ''}`}>
                    <span></span>
                    <span></span>
                    <span></span>
                  </span>
                </button>

                {/* Desktop Navigation */}
                <div className="navbar-collapse d-none d-lg-flex">
                  <ul className="navbar-nav me-auto">
                    <li className="nav-item">
                      <Link
                        href="/"
                        className={`nav-link ${isActive('/') ? 'active' : ''}`}
                      >
                        Home
                      </Link>
                    </li>
                    <li className="nav-item">
                      <Link
                        href="/about"
                        className={`nav-link ${isActive('/about') ? 'active' : ''}`}
                      >
                        About
                      </Link>
                    </li>
                    
                    {/* Services Dropdown */}
                    <li
                      className={`nav-item dropdown ${activeDropdown === 'services' ? 'show' : ''}`}
                      onMouseEnter={() => handleDropdownEnter('services')}
                      onMouseLeave={handleDropdownLeave}
                    >
                      <Link
                        href="/service"
                        className={`nav-link dropdown-toggle ${isActive('/service') ? 'active' : ''}`}
                        role="button"
                        aria-expanded={activeDropdown === 'services'}
                        onKeyDown={(e) => handleDropdownKeyDown(e, 'services')}
                        tabIndex={0}
                      >
                        Services
                        <i className="fa-solid fa-chevron-down ms-1"></i>
                      </Link>
                      <ul
                        className={`dropdown-menu ${activeDropdown === 'services' ? 'show' : ''}`}
                        role="menu"
                        aria-labelledby="services-dropdown"
                      >
                        {servicesItems.map((item, index) => (
                          <li key={item.href} role="none">
                            <Link
                              href={item.href}
                              className="dropdown-item"
                              role="menuitem"
                              tabIndex={activeDropdown === 'services' ? 0 : -1}
                              onKeyDown={handleDropdownItemKeyDown}
                            >
                              <i className={`fa-solid ${item.icon} me-2`}></i>
                              {item.label}
                            </Link>
                          </li>
                        ))}
                      </ul>
                    </li>

                    {/* Projects Dropdown */}
                    <li
                      className={`nav-item dropdown ${activeDropdown === 'projects' ? 'show' : ''}`}
                      onMouseEnter={() => handleDropdownEnter('projects')}
                      onMouseLeave={handleDropdownLeave}
                    >
                      <Link
                        href="/projects"
                        className={`nav-link dropdown-toggle ${isActive('/projects') ? 'active' : ''}`}
                        role="button"
                        aria-expanded={activeDropdown === 'projects'}
                        onKeyDown={(e) => handleDropdownKeyDown(e, 'projects')}
                        tabIndex={0}
                      >
                        Projects
                        <i className="fa-solid fa-chevron-down ms-1"></i>
                      </Link>
                      <ul
                        className={`dropdown-menu ${activeDropdown === 'projects' ? 'show' : ''}`}
                        role="menu"
                        aria-labelledby="projects-dropdown"
                      >
                        {projectsItems.map((item, index) => (
                          <li key={item.href} role="none">
                            <Link
                              href={item.href}
                              className="dropdown-item"
                              role="menuitem"
                              tabIndex={activeDropdown === 'projects' ? 0 : -1}
                              onKeyDown={handleDropdownItemKeyDown}
                            >
                              <i className={`fa-solid ${item.icon} me-2`}></i>
                              {item.label}
                            </Link>
                          </li>
                        ))}
                      </ul>
                    </li>

                    <li className="nav-item">
                      <Link
                        href="/blog"
                        className={`nav-link ${isActive('/blog') ? 'active' : ''}`}
                      >
                        Blog
                      </Link>
                    </li>
                    <li className="nav-item">
                      <Link
                        href="/contact"
                        className={`nav-link ${isActive('/contact') ? 'active' : ''}`}
                      >
                        Contact
                      </Link>
                    </li>
                  </ul>

                  {/* CTA Button */}
                  <div className="navbar-nav">
                    <Link href="/contact" className="btn btn-primary nav-cta">
                      Start Digital Transformation
                      <i className="fa-solid fa-arrow-right ms-2"></i>
                    </Link>
                  </div>
                </div>
              </nav>
            </div>
          </div>
        </div>
      </header>

      {/* Mobile Menu Overlay */}
      <div
        className={`mobile-overlay ${isMobileMenuOpen ? 'show' : ''}`}
        onClick={() => setIsMobileMenuOpen(false)}
        aria-hidden="true"
      ></div>

      {/* Mobile Navigation Menu */}
      <div className={`mobile-menu ${isMobileMenuOpen ? 'show' : ''}`}>
        <div className="mobile-menu-header">
          <Link href="/" className="mobile-logo" onClick={() => setIsMobileMenuOpen(false)}>
            <img
              src="/assets/img/logo/logo_full.svg"
              alt="Motshwanelo IT Consulting"
              className="mobile-logo-img"
            />
          </Link>
          <button
            className="mobile-close"
            onClick={() => setIsMobileMenuOpen(false)}
            aria-label="Close menu"
          >
            <i className="fa-solid fa-times"></i>
          </button>
        </div>

        <nav className="mobile-nav">
          <ul className="mobile-nav-list">
            <li>
              <Link
                href="/"
                className={`mobile-nav-link ${isActive('/') ? 'active' : ''}`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <i className="fa-solid fa-home"></i>
                Home
              </Link>
            </li>
            <li>
              <Link
                href="/about"
                className={`mobile-nav-link ${isActive('/about') ? 'active' : ''}`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <i className="fa-solid fa-users"></i>
                About
              </Link>
            </li>
            
            {/* Mobile Services */}
            <li className="mobile-dropdown">
              <div className="mobile-dropdown-header">
                <Link
                  href="/service"
                  className={`mobile-nav-link ${isActive('/service') ? 'active' : ''}`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <i className="fa-solid fa-cogs"></i>
                  Services
                </Link>
                <button
                  className="mobile-dropdown-toggle"
                  onClick={() => toggleMobileDropdown('services')}
                  aria-expanded={mobileDropdowns.services}
                  aria-label="Toggle services menu"
                >
                  <i className={`fa-solid fa-chevron-down ${mobileDropdowns.services ? 'rotate' : ''}`}></i>
                </button>
              </div>
              <ul className={`mobile-dropdown-menu ${mobileDropdowns.services ? 'show' : ''}`}>
                {servicesItems.map((item) => (
                  <li key={item.href}>
                    <Link
                      href={item.href}
                      className="mobile-dropdown-link"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <i className={`fa-solid ${item.icon}`}></i>
                      {item.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </li>

            {/* Mobile Projects */}
            <li className="mobile-dropdown">
              <div className="mobile-dropdown-header">
                <Link
                  href="/projects"
                  className={`mobile-nav-link ${isActive('/projects') ? 'active' : ''}`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <i className="fa-solid fa-briefcase"></i>
                  Projects
                </Link>
                <button
                  className="mobile-dropdown-toggle"
                  onClick={() => toggleMobileDropdown('projects')}
                  aria-expanded={mobileDropdowns.projects}
                  aria-label="Toggle projects menu"
                >
                  <i className={`fa-solid fa-chevron-down ${mobileDropdowns.projects ? 'rotate' : ''}`}></i>
                </button>
              </div>
              <ul className={`mobile-dropdown-menu ${mobileDropdowns.projects ? 'show' : ''}`}>
                {projectsItems.map((item) => (
                  <li key={item.href}>
                    <Link
                      href={item.href}
                      className="mobile-dropdown-link"
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      <i className={`fa-solid ${item.icon}`}></i>
                      {item.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </li>

            <li>
              <Link
                href="/blog"
                className={`mobile-nav-link ${isActive('/blog') ? 'active' : ''}`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <i className="fa-solid fa-blog"></i>
                Blog
              </Link>
            </li>
            <li>
              <Link
                href="/contact"
                className={`mobile-nav-link ${isActive('/contact') ? 'active' : ''}`}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                <i className="fa-solid fa-envelope"></i>
                Contact
              </Link>
            </li>
          </ul>

          {/* Mobile CTA */}
          <div className="mobile-cta">
            <Link
              href="/contact"
              className="btn btn-primary w-100"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Start Digital Transformation
              <i className="fa-solid fa-arrow-right ms-2"></i>
            </Link>
          </div>
        </nav>
      </div>
    </>
  );
};

export default NewModernHeader;
